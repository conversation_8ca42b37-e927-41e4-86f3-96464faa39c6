import additionalServiceApi from '../../../api/modules/additionalService.js';
import Session from '../../../common/Session.js';
import utils from '../../utils/util.js';

Page({
  data: {
    userInfo: null,
    orderDetailId: null,

    // 追加服务列表
    additionalServices: [],
    loading: false,

    // 筛选条件
    currentTab: 'all',
    tabs: [
      { key: 'all', label: '全部' },
      { key: 'pending_confirm', label: '待确认' },
      { key: 'confirmed', label: '已确认' },
      { key: 'paid', label: '已付款' },
      { key: 'rejected', label: '已拒绝' },
    ],

    // 状态映射
    statusMap: {
      pending_confirm: { text: '待确认', color: '#ff9500', desc: '等待员工确认' },
      confirmed: { text: '已确认', color: '#007aff', desc: '请尽快完成支付' },
      paid: { text: '已付款', color: '#34c759', desc: '服务进行中' },
      rejected: { text: '已拒绝', color: '#ff3b30', desc: '申请被拒绝' },
    },

    // 模态框
    showModal: false,
    modalTitle: '',
    modalContent: '',
    modalButtons: [],
  },

  onLoad(options) {
    const { orderDetailId } = options;
    const userInfo = Session.getUser();

    if (!userInfo) {
      wx.showToast({
        title: '请先登录',
        icon: 'none',
      });
      wx.navigateBack();
      return;
    }

    this.setData({
      userInfo,
      orderDetailId: parseInt(orderDetailId),
    });

    this.loadAdditionalServices();
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadAdditionalServices();
  },

  /**
   * 切换标签
   */
  switchTab(e) {
    const { key } = e.currentTarget.dataset;
    this.setData({ currentTab: key });
    this.loadAdditionalServices();
  },

  /**
   * 加载追加服务列表
   */
  async loadAdditionalServices() {
    try {
      this.setData({ loading: true });

      const { orderDetailId, currentTab } = this.data;
      const params = currentTab === 'all' ? {} : { status: currentTab };

      const services = await additionalServiceApi.list(orderDetailId, params);

      // 格式化数据
      const formattedServices = (services || []).map((item) => {
        return {
          ...item,
          createdAt: item.createdAt ? utils.formatNormalDate(item.createdAt) : '',
          confirmTime: item.confirmTime ? utils.formatNormalDate(item.confirmTime) : '',
          statusInfo: this.data.statusMap[item.status] || { text: item.status, color: '#999', desc: '' },
        };
      });

      this.setData({
        additionalServices: formattedServices,
      });
    } catch (error) {
      console.error('加载追加服务列表失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none',
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 查看详情
   */
  viewDetail(e) {
    const { id } = e.currentTarget.dataset;
    const { orderDetailId } = this.data;

    wx.navigateTo({
      url: `/pages/additionalService/detail/index?orderDetailId=${orderDetailId}&id=${id}`,
    });
  },

  /**
   * 去支付
   */
  goPay(e) {
    const { id } = e.currentTarget.dataset;
    const { orderDetailId } = this.data;

    wx.navigateTo({
      url: `/pages/additionalService/pay/index?orderDetailId=${orderDetailId}&id=${id}`,
    });
  },

  /**
   * 删除追加服务
   */
  deleteService(e) {
    const { id, name } = e.currentTarget.dataset;

    wx.showModal({
      title: '删除确认',
      content: `确定要删除追加服务"${name}"吗？删除后无法恢复。`,
      success: (res) => {
        if (res.confirm) {
          this.performDeleteService(id);
        }
      }
    });
  },

  /**
   * 执行删除追加服务
   */
  async performDeleteService(id) {
    try {
      wx.showLoading({ title: '删除中...' });

      const { orderDetailId, userInfo } = this.data;

      if (!userInfo || !userInfo.id) {
        throw new Error('用户信息不存在');
      }

      // 调用删除API
      await additionalServiceApi.delete(orderDetailId, id, userInfo.id);

      // 重新加载列表
      await this.loadAdditionalServices();

      wx.hideLoading();
      wx.showToast({
        title: '删除成功',
        icon: 'success'
      });

    } catch (error) {
      wx.hideLoading();
      console.error('删除追加服务失败:', error);
      wx.showToast({
        title: '删除失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 申请新的追加服务
   */
  applyNewService() {
    const { orderDetailId } = this.data;

    wx.navigateTo({
      url: `/pages/additionalService/apply/index?orderDetailId=${orderDetailId}`,
    });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadAdditionalServices().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 模态框确认
   */
  handleModalConfirm() {
    this.setData({ showModal: false });
  },

  /**
   * 模态框取消
   */
  handleModalCancel() {
    this.setData({ showModal: false });
  },
});
