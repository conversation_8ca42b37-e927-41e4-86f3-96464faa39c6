/* 页面容器 */
.container {
  background-color: #f4f4f4;
  min-height: 100vh;
  padding: 20rpx 20rpx 120rpx;
}

/* 状态卡片样式 */
.status-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  border-left: 6rpx solid #ff4391;
}

.status-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
}

.status-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.status-text {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.status-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

/* 信息区块样式 */
.info-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

/* 信息列表样式 */
.info-list {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  line-height: 60rpx;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  flex-shrink: 0;
  min-width: 200rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  text-align: right;
  flex: 1;
}

.info-value.link {
  color: #2f83ff;
  text-decoration: underline;
}

/* 服务列表样式 */
.service-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.service-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border-left: 4rpx solid #2f83ff;
}

.service-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.service-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.service-spec {
  font-size: 24rpx;
  color: #666;
}

.service-total {
  font-size: 28rpx;
  font-weight: bold;
  color: #ff4391;
  margin-left: 20rpx;
}

/* 价格明细样式 */
.price-details {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  line-height: 60rpx;
}

.price-row.discount {
  color: #ff4391;
}

.price-row.total {
  padding-top: 20rpx;
  border-top: 2rpx solid #e4e4e4;
  font-weight: bold;
}

.price-label {
  font-size: 28rpx;
  color: #666;
  min-width: 200rpx;
}

.price-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  text-align: right;
}

.price-value.total-price {
  font-size: 32rpx;
  color: #ff4391;
  font-weight: bold;
}

/* 优惠信息样式 */
.discount-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.discount-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  border-left: 4rpx solid #ff4391;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.discount-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.discount-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.discount-type {
  font-size: 28rpx;
  color: #ff4391;
  font-weight: bold;
}

.discount-amount {
  font-size: 28rpx;
  color: #ff4391;
  font-weight: bold;
}

.discount-detail {
  margin-top: 5rpx;
}

.discount-rate {
  font-size: 24rpx;
  color: #666;
}

.discount-name {
  margin-top: 5rpx;
}

.name-text {
  font-size: 24rpx;
  color: #999;
}

/* 客户信息样式 */
.customer-info {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.customer-info .info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  line-height: 60rpx;
}

.customer-info .info-label {
  font-size: 28rpx;
  color: #666;
  flex-shrink: 0;
  min-width: 200rpx;
}

.customer-info .info-value {
  font-size: 28rpx;
  color: #333;
  text-align: right;
  flex: 1;
  word-break: break-all;
}

/* 备注信息样式 */
.remark-content {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
  border-left: 4rpx solid #ff4391;
}

.remark-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  word-break: break-all;
  white-space: pre-wrap;
}

/* 操作按钮样式 */
.action-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx;
  background-color: #fff;
  border-top: 1rpx solid #e5e5e5;
  display: flex;
  gap: 20rpx;
  z-index: 100;
  box-shadow: 0 -2rpx 20rpx rgba(0, 0, 0, 0.08);
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 30rpx;
  font-weight: bold;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-btn.primary {
  background: linear-gradient(135deg, #ff4391 0%, #ff7ba7 100%);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(255, 67, 145, 0.3);
}

.action-btn.primary:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(255, 67, 145, 0.4);
}

.action-btn.secondary {
  background-color: #f0f0f0;
  color: #666;
  border: 1rpx solid #e0e0e0;
}

.action-btn.secondary:active {
  background-color: #e8e8e8;
  transform: translateY(2rpx);
}

.action-btn.danger {
  background: linear-gradient(135deg, #ff3b30 0%, #ff6b5a 100%);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(255, 59, 48, 0.3);
}

.action-btn.danger:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(255, 59, 48, 0.4);
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}
